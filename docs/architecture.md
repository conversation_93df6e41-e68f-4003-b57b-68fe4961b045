# 舆情监控Admin系统架构文档

## 1. 系统总览

### 1.1 系统简介
舆情监控Admin系统是一个基于微服务架构的企业级舆情数据管理平台，提供数据采集配置、用户权限管理、内容审核、数据分析等功能。

### 1.2 技术栈
- **后端框架**: Spring Boot 3.5.4 + Spring Cloud
- **数据库**: MySQL 8.0 + Redis
- **消息队列**: RabbitMQ / Apache Kafka
- **服务注册与发现**: Nacos / Eureka
- **API网关**: Spring Cloud Gateway
- **认证授权**: Spring Security + JWT
- **监控**: Spring Boot Actuator + Prometheus + Grafana
- **文档**: Swagger/OpenAPI 3.0

### 1.3 系统特性
- 多级用户权限控制（一级用户、二级用户）
- 分布式微服务架构
- 实时数据采集与处理
- 灵活的审核工作流
- 可扩展的数据源配置

## 2. 微服务拆分策略

### 2.1 服务拆分原则
- **业务边界清晰**: 按业务领域拆分
- **数据独立**: 每个服务拥有独立的数据库
- **松耦合**: 服务间通过API通信
- **高内聚**: 相关功能聚合在同一服务内

### 2.2 微服务列表

#### 2.2.1 基础服务
1. **用户认证服务 (auth-service)**
2. **用户管理服务 (user-service)**
3. **权限管理服务 (permission-service)**

#### 2.2.2 业务服务
4. **采集配置服务 (collection-config-service)**
5. **数据采集服务 (data-collection-service)**
6. **内容审核服务 (content-audit-service)**
7. **舆情分析服务 (sentiment-analysis-service)**
8. **通知服务 (notification-service)**

#### 2.2.3 支撑服务
9. **API网关服务 (gateway-service)**
10. **配置中心服务 (config-service)**
11. **监控服务 (monitor-service)**

## 3. 详细服务设计

### 3.1 用户认证服务 (auth-service)

#### 3.1.1 功能职责
- 用户登录认证
- JWT Token生成与验证
- 密码加密与验证
- 登录日志记录
- 单点登录(SSO)支持

#### 3.1.2 核心接口设计

**登录接口**
```
POST /auth/login
Request: {
  "username": "string",
  "password": "string",
  "captcha": "string"
}
Response: {
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresIn": 7200,
    "userInfo": {
      "userId": "string",
      "username": "string",
      "userLevel": 1,
      "permissions": ["string"]
    }
  }
}
```

**Token验证接口**
```
POST /auth/verify
Request: {
  "token": "jwt_token"
}
Response: {
  "code": 200,
  "message": "Token有效",
  "data": {
    "valid": true,
    "userId": "string",
    "permissions": ["string"]
  }
}
```

**Token刷新接口**
```
POST /auth/refresh
Request: {
  "refreshToken": "refresh_token"
}
Response: {
  "code": 200,
  "message": "刷新成功",
  "data": {
    "token": "new_jwt_token",
    "expiresIn": 7200
  }
}
```

#### 3.1.3 数据库设计

**用户登录日志表 (auth_login_log)**
```sql
CREATE TABLE auth_login_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    login_ip VARCHAR(50) COMMENT '登录IP',
    login_location VARCHAR(200) COMMENT '登录地点',
    browser VARCHAR(100) COMMENT '浏览器',
    os VARCHAR(100) COMMENT '操作系统',
    login_time DATETIME NOT NULL COMMENT '登录时间',
    login_status TINYINT DEFAULT 1 COMMENT '登录状态 1成功 0失败',
    error_message VARCHAR(500) COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time)
);
```

**Token黑名单表 (auth_token_blacklist)**
```sql
CREATE TABLE auth_token_blacklist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    token_id VARCHAR(100) NOT NULL COMMENT 'Token ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    token VARCHAR(1000) NOT NULL COMMENT 'Token内容',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_token_id (token_id),
    INDEX idx_user_id (user_id),
    INDEX idx_expire_time (expire_time)
);
```

#### 3.1.4 依赖组件
- **Redis**: Token缓存、登录限制
- **MySQL**: 登录日志存储
- **Spring Security**: 安全框架
- **JWT**: Token生成

### 3.2 用户管理服务 (user-service)

#### 3.2.1 功能职责
- 用户信息管理
- 用户层级管理（一级用户、二级用户）
- 用户状态管理
- 用户组织架构管理

#### 3.2.2 核心接口设计

**创建用户接口**
```
POST /user/create
Request: {
  "username": "string",
  "password": "string",
  "email": "string",
  "phone": "string",
  "realName": "string",
  "userLevel": 1,
  "parentUserId": "string",
  "organizationId": "string",
  "roleIds": ["string"]
}
Response: {
  "code": 200,
  "message": "创建成功",
  "data": {
    "userId": "string"
  }
}
```

**用户列表查询接口**
```
GET /user/list?page=1&size=10&userLevel=1&status=1&keyword=test
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 100,
    "list": [{
      "userId": "string",
      "username": "string",
      "email": "string",
      "realName": "string",
      "userLevel": 1,
      "status": 1,
      "organizationName": "string",
      "createdTime": "2024-01-01 10:00:00"
    }]
  }
}
```

**用户详情接口**
```
GET /user/{userId}
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "userId": "string",
    "username": "string",
    "email": "string",
    "phone": "string",
    "realName": "string",
    "userLevel": 1,
    "status": 1,
    "parentUserId": "string",
    "organizationId": "string",
    "roles": [{
      "roleId": "string",
      "roleName": "string"
    }],
    "createdTime": "2024-01-01 10:00:00",
    "updatedTime": "2024-01-01 10:00:00"
  }
}
```

#### 3.2.3 数据库设计

**用户基础信息表 (sys_user)**
```sql
CREATE TABLE sys_user (
    user_id VARCHAR(64) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(200) NOT NULL COMMENT '密码',
    email VARCHAR(200) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(100) COMMENT '真实姓名',
    avatar VARCHAR(500) COMMENT '头像URL',
    user_level TINYINT NOT NULL DEFAULT 2 COMMENT '用户级别 1一级用户 2二级用户',
    parent_user_id VARCHAR(64) COMMENT '上级用户ID',
    organization_id VARCHAR(64) COMMENT '组织ID',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记 0未删除 1已删除',
    INDEX idx_username (username),
    INDEX idx_user_level (user_level),
    INDEX idx_parent_user_id (parent_user_id),
    INDEX idx_organization_id (organization_id),
    INDEX idx_status (status)
);
```
