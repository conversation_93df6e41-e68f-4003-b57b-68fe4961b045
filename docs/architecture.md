# 舆情监控Admin系统架构文档

## 1. 系统总览

### 1.1 系统简介
舆情监控Admin系统是一个基于微服务架构的企业级舆情数据管理平台，提供数据采集配置、用户权限管理、内容审核、数据分析等功能。

### 1.2 技术栈
- **后端框架**: Spring Boot 3.5.4 + Spring Cloud
- **数据库**: MySQL 8.0 + Redis
- **消息队列**: RabbitMQ / Apache Kafka
- **服务注册与发现**: Nacos / Eureka
- **API网关**: Spring Cloud Gateway
- **认证授权**: Spring Security + JWT
- **监控**: Spring Boot Actuator + Prometheus + Grafana
- **文档**: Swagger/OpenAPI 3.0

### 1.3 系统特性
- 多级用户权限控制（一级用户、二级用户）
- 分布式微服务架构
- 实时数据采集与处理
- 灵活的审核工作流
- 可扩展的数据源配置

## 2. 微服务拆分策略

### 2.1 服务拆分原则
- **业务边界清晰**: 按业务领域拆分
- **数据独立**: 每个服务拥有独立的数据库
- **松耦合**: 服务间通过API通信
- **高内聚**: 相关功能聚合在同一服务内

### 2.2 微服务列表

#### 2.2.1 基础服务
1. **用户认证服务 (auth-service)**
2. **用户管理服务 (user-service)**
3. **权限管理服务 (permission-service)**

#### 2.2.2 业务服务
4. **采集配置服务 (collection-config-service)**
5. **数据采集服务 (data-collection-service)**
6. **内容审核服务 (content-audit-service)**
7. **舆情分析服务 (sentiment-analysis-service)**
8. **通知服务 (notification-service)**

#### 2.2.3 支撑服务
9. **API网关服务 (gateway-service)**
10. **配置中心服务 (config-service)**
11. **监控服务 (monitor-service)**

## 3. 详细服务设计

### 3.1 用户认证服务 (auth-service)

#### 3.1.1 功能职责
- 用户登录认证
- JWT Token生成与验证
- 密码加密与验证
- 登录日志记录
- 单点登录(SSO)支持

#### 3.1.2 核心接口设计

**登录接口**
```
POST /auth/login
Request: {
  "username": "string",
  "password": "string",
  "captcha": "string"
}
Response: {
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresIn": 7200,
    "userInfo": {
      "userId": "string",
      "username": "string",
      "userLevel": 1,
      "permissions": ["string"]
    }
  }
}
```

**Token验证接口**
```
POST /auth/verify
Request: {
  "token": "jwt_token"
}
Response: {
  "code": 200,
  "message": "Token有效",
  "data": {
    "valid": true,
    "userId": "string",
    "permissions": ["string"]
  }
}
```

**Token刷新接口**
```
POST /auth/refresh
Request: {
  "refreshToken": "refresh_token"
}
Response: {
  "code": 200,
  "message": "刷新成功",
  "data": {
    "token": "new_jwt_token",
    "expiresIn": 7200
  }
}
```

#### 3.1.3 数据库设计

**用户登录日志表 (auth_login_log)**
```sql
CREATE TABLE auth_login_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    login_ip VARCHAR(50) COMMENT '登录IP',
    login_location VARCHAR(200) COMMENT '登录地点',
    browser VARCHAR(100) COMMENT '浏览器',
    os VARCHAR(100) COMMENT '操作系统',
    login_time DATETIME NOT NULL COMMENT '登录时间',
    login_status TINYINT DEFAULT 1 COMMENT '登录状态 1成功 0失败',
    error_message VARCHAR(500) COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time)
);
```

**Token黑名单表 (auth_token_blacklist)**
```sql
CREATE TABLE auth_token_blacklist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    token_id VARCHAR(100) NOT NULL COMMENT 'Token ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    token VARCHAR(1000) NOT NULL COMMENT 'Token内容',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_token_id (token_id),
    INDEX idx_user_id (user_id),
    INDEX idx_expire_time (expire_time)
);
```

#### 3.1.4 依赖组件
- **Redis**: Token缓存、登录限制
- **MySQL**: 登录日志存储
- **Spring Security**: 安全框架
- **JWT**: Token生成

### 3.2 用户管理服务 (user-service)

#### 3.2.1 功能职责
- 用户信息管理
- 用户层级管理（一级用户、二级用户）
- 用户状态管理
- 用户组织架构管理

#### 3.2.2 核心接口设计

**创建用户接口**
```
POST /user/create
Request: {
  "username": "string",
  "password": "string",
  "email": "string",
  "phone": "string",
  "realName": "string",
  "userLevel": 1,
  "parentUserId": "string",
  "organizationId": "string",
  "roleIds": ["string"]
}
Response: {
  "code": 200,
  "message": "创建成功",
  "data": {
    "userId": "string"
  }
}
```

**用户列表查询接口**
```
GET /user/list?page=1&size=10&userLevel=1&status=1&keyword=test
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 100,
    "list": [{
      "userId": "string",
      "username": "string",
      "email": "string",
      "realName": "string",
      "userLevel": 1,
      "status": 1,
      "organizationName": "string",
      "createdTime": "2024-01-01 10:00:00"
    }]
  }
}
```

**用户详情接口**
```
GET /user/{userId}
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "userId": "string",
    "username": "string",
    "email": "string",
    "phone": "string",
    "realName": "string",
    "userLevel": 1,
    "status": 1,
    "parentUserId": "string",
    "organizationId": "string",
    "roles": [{
      "roleId": "string",
      "roleName": "string"
    }],
    "createdTime": "2024-01-01 10:00:00",
    "updatedTime": "2024-01-01 10:00:00"
  }
}
```

#### 3.2.3 数据库设计

**用户基础信息表 (sys_user)**
```sql
CREATE TABLE sys_user (
    user_id VARCHAR(64) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(200) NOT NULL COMMENT '密码',
    email VARCHAR(200) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(100) COMMENT '真实姓名',
    avatar VARCHAR(500) COMMENT '头像URL',
    user_level TINYINT NOT NULL DEFAULT 2 COMMENT '用户级别 1一级用户 2二级用户',
    parent_user_id VARCHAR(64) COMMENT '上级用户ID',
    organization_id VARCHAR(64) COMMENT '组织ID',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记 0未删除 1已删除',
    INDEX idx_username (username),
    INDEX idx_user_level (user_level),
    INDEX idx_parent_user_id (parent_user_id),
    INDEX idx_organization_id (organization_id),
    INDEX idx_status (status)
);
```

**组织架构表 (sys_organization)**
```sql
CREATE TABLE sys_organization (
    organization_id VARCHAR(64) PRIMARY KEY COMMENT '组织ID',
    organization_name VARCHAR(200) NOT NULL COMMENT '组织名称',
    parent_id VARCHAR(64) COMMENT '父级组织ID',
    organization_code VARCHAR(100) COMMENT '组织编码',
    organization_type TINYINT DEFAULT 1 COMMENT '组织类型 1部门 2公司',
    sort_order INT DEFAULT 0 COMMENT '排序',
    description TEXT COMMENT '描述',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_parent_id (parent_id),
    INDEX idx_organization_code (organization_code)
);
```

**用户角色关联表 (sys_user_role)**
```sql
CREATE TABLE sys_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
);
```

#### 3.2.4 依赖组件
- **MySQL**: 用户数据存储
- **Redis**: 用户信息缓存
- **权限管理服务**: 角色权限验证

### 3.3 权限管理服务 (permission-service)

#### 3.3.1 功能职责
- 角色管理
- 权限管理
- 角色权限关联
- 权限验证
- 菜单权限控制

#### 3.3.2 核心接口设计

**创建角色接口**
```
POST /permission/role/create
Request: {
  "roleName": "string",
  "roleCode": "string",
  "description": "string",
  "userLevel": 1,
  "permissionIds": ["string"]
}
Response: {
  "code": 200,
  "message": "创建成功",
  "data": {
    "roleId": "string"
  }
}
```

**权限验证接口**
```
POST /permission/verify
Request: {
  "userId": "string",
  "resource": "string",
  "action": "string"
}
Response: {
  "code": 200,
  "message": "验证成功",
  "data": {
    "hasPermission": true
  }
}
```

**用户菜单权限接口**
```
GET /permission/menu/{userId}
Response: {
  "code": 200,
  "message": "查询成功",
  "data": [{
    "menuId": "string",
    "menuName": "string",
    "menuCode": "string",
    "parentId": "string",
    "path": "string",
    "component": "string",
    "icon": "string",
    "sortOrder": 1,
    "children": []
  }]
}
```

#### 3.3.3 数据库设计

**角色表 (sys_role)**
```sql
CREATE TABLE sys_role (
    role_id VARCHAR(64) PRIMARY KEY COMMENT '角色ID',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(100) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    user_level TINYINT NOT NULL COMMENT '适用用户级别 1一级用户 2二级用户',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_role_code (role_code),
    INDEX idx_user_level (user_level)
);
```

**权限表 (sys_permission)**
```sql
CREATE TABLE sys_permission (
    permission_id VARCHAR(64) PRIMARY KEY COMMENT '权限ID',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    resource VARCHAR(200) NOT NULL COMMENT '资源标识',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    permission_type TINYINT DEFAULT 1 COMMENT '权限类型 1菜单 2按钮 3接口',
    parent_id VARCHAR(64) COMMENT '父级权限ID',
    path VARCHAR(500) COMMENT '路径',
    component VARCHAR(200) COMMENT '组件',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_type (permission_type)
);
```

**角色权限关联表 (sys_role_permission)**
```sql
CREATE TABLE sys_role_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
    permission_id VARCHAR(64) NOT NULL COMMENT '权限ID',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
);
```

#### 3.3.4 依赖组件
- **MySQL**: 权限数据存储
- **Redis**: 权限信息缓存
- **用户管理服务**: 用户信息获取

### 3.4 采集配置服务 (collection-config-service)

#### 3.4.1 功能职责
- 数据源配置管理
- 采集任务配置
- 采集规则配置
- 采集计划管理
- 配置版本控制

#### 3.4.2 核心接口设计

**创建数据源接口**
```
POST /collection/datasource/create
Request: {
  "sourceName": "string",
  "sourceType": "WEB",
  "sourceUrl": "string",
  "description": "string",
  "headers": {
    "User-Agent": "string"
  },
  "params": {
    "timeout": 30000
  },
  "status": 1
}
Response: {
  "code": 200,
  "message": "创建成功",
  "data": {
    "sourceId": "string"
  }
}
```

**创建采集任务接口**
```
POST /collection/task/create
Request: {
  "taskName": "string",
  "sourceId": "string",
  "collectionRules": [{
    "fieldName": "title",
    "selector": "h1.title",
    "selectorType": "CSS",
    "required": true
  }],
  "scheduleConfig": {
    "scheduleType": "CRON",
    "cronExpression": "0 */10 * * * ?",
    "enabled": true
  },
  "filterRules": [{
    "field": "title",
    "operator": "CONTAINS",
    "value": "关键词"
  }]
}
Response: {
  "code": 200,
  "message": "创建成功",
  "data": {
    "taskId": "string"
  }
}
```

**任务列表查询接口**
```
GET /collection/task/list?page=1&size=10&status=1&sourceId=xxx
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 50,
    "list": [{
      "taskId": "string",
      "taskName": "string",
      "sourceName": "string",
      "status": 1,
      "lastExecuteTime": "2024-01-01 10:00:00",
      "nextExecuteTime": "2024-01-01 10:10:00",
      "executeCount": 100,
      "successCount": 95,
      "failCount": 5
    }]
  }
}
```

#### 3.4.3 数据库设计

**数据源配置表 (collection_datasource)**
```sql
CREATE TABLE collection_datasource (
    source_id VARCHAR(64) PRIMARY KEY COMMENT '数据源ID',
    source_name VARCHAR(200) NOT NULL COMMENT '数据源名称',
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型 WEB/API/RSS/SOCIAL',
    source_url VARCHAR(1000) NOT NULL COMMENT '数据源地址',
    description TEXT COMMENT '描述',
    headers JSON COMMENT '请求头配置',
    params JSON COMMENT '参数配置',
    auth_config JSON COMMENT '认证配置',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_source_type (source_type),
    INDEX idx_status (status)
);
```

**采集任务表 (collection_task)**
```sql
CREATE TABLE collection_task (
    task_id VARCHAR(64) PRIMARY KEY COMMENT '任务ID',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    source_id VARCHAR(64) NOT NULL COMMENT '数据源ID',
    task_type TINYINT DEFAULT 1 COMMENT '任务类型 1定时 2手动',
    schedule_config JSON COMMENT '调度配置',
    collection_rules JSON COMMENT '采集规则',
    filter_rules JSON COMMENT '过滤规则',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    last_execute_time DATETIME COMMENT '最后执行时间',
    next_execute_time DATETIME COMMENT '下次执行时间',
    execute_count INT DEFAULT 0 COMMENT '执行次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    fail_count INT DEFAULT 0 COMMENT '失败次数',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_source_id (source_id),
    INDEX idx_status (status),
    INDEX idx_next_execute_time (next_execute_time)
);
```

**采集规则模板表 (collection_rule_template)**
```sql
CREATE TABLE collection_rule_template (
    template_id VARCHAR(64) PRIMARY KEY COMMENT '模板ID',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    source_type VARCHAR(50) NOT NULL COMMENT '适用数据源类型',
    rule_config JSON NOT NULL COMMENT '规则配置',
    description TEXT COMMENT '描述',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统模板 1是 0否',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_source_type (source_type),
    INDEX idx_is_system (is_system)
);
```

#### 3.4.4 依赖组件
- **MySQL**: 配置数据存储
- **Redis**: 配置缓存
- **Quartz**: 任务调度
- **数据采集服务**: 任务执行

### 3.5 数据采集服务 (data-collection-service)

#### 3.5.1 功能职责
- 执行采集任务
- 数据抓取与解析
- 数据清洗与标准化
- 采集结果存储
- 采集状态监控

#### 3.5.2 核心接口设计

**手动执行采集任务接口**
```
POST /collection/execute/{taskId}
Request: {
  "executeType": "MANUAL",
  "params": {
    "startUrl": "string",
    "maxPages": 10
  }
}
Response: {
  "code": 200,
  "message": "执行成功",
  "data": {
    "executeId": "string",
    "status": "RUNNING"
  }
}
```

**查询采集执行状态接口**
```
GET /collection/execute/status/{executeId}
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "executeId": "string",
    "taskId": "string",
    "status": "COMPLETED",
    "startTime": "2024-01-01 10:00:00",
    "endTime": "2024-01-01 10:05:00",
    "totalCount": 100,
    "successCount": 95,
    "failCount": 5,
    "errorMessage": "string"
  }
}
```

**采集数据查询接口**
```
GET /collection/data/list?taskId=xxx&page=1&size=10&startTime=xxx&endTime=xxx
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 1000,
    "list": [{
      "dataId": "string",
      "taskId": "string",
      "sourceUrl": "string",
      "title": "string",
      "content": "string",
      "author": "string",
      "publishTime": "2024-01-01 09:00:00",
      "collectTime": "2024-01-01 10:00:00",
      "status": 1
    }]
  }
}
```

#### 3.5.3 数据库设计

**采集执行记录表 (collection_execute_log)**
```sql
CREATE TABLE collection_execute_log (
    execute_id VARCHAR(64) PRIMARY KEY COMMENT '执行ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    execute_type TINYINT DEFAULT 1 COMMENT '执行类型 1定时 2手动',
    status TINYINT DEFAULT 1 COMMENT '执行状态 1运行中 2成功 3失败 4取消',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    total_count INT DEFAULT 0 COMMENT '总数量',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    fail_count INT DEFAULT 0 COMMENT '失败数量',
    error_message TEXT COMMENT '错误信息',
    execute_params JSON COMMENT '执行参数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

**采集数据表 (collection_data)**
```sql
CREATE TABLE collection_data (
    data_id VARCHAR(64) PRIMARY KEY COMMENT '数据ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    execute_id VARCHAR(64) NOT NULL COMMENT '执行ID',
    source_url VARCHAR(1000) NOT NULL COMMENT '来源URL',
    title VARCHAR(500) COMMENT '标题',
    content LONGTEXT COMMENT '内容',
    author VARCHAR(200) COMMENT '作者',
    publish_time DATETIME COMMENT '发布时间',
    collect_time DATETIME NOT NULL COMMENT '采集时间',
    raw_data JSON COMMENT '原始数据',
    status TINYINT DEFAULT 1 COMMENT '状态 1正常 2待审核 3已删除',
    audit_status TINYINT DEFAULT 0 COMMENT '审核状态 0未审核 1通过 2拒绝',
    sentiment_score DECIMAL(3,2) COMMENT '情感分数',
    keywords JSON COMMENT '关键词',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_execute_id (execute_id),
    INDEX idx_publish_time (publish_time),
    INDEX idx_collect_time (collect_time),
    INDEX idx_status (status),
    INDEX idx_audit_status (audit_status),
    FULLTEXT idx_title_content (title, content)
);
```

**采集错误日志表 (collection_error_log)**
```sql
CREATE TABLE collection_error_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    execute_id VARCHAR(64) NOT NULL COMMENT '执行ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    error_url VARCHAR(1000) COMMENT '错误URL',
    error_type VARCHAR(100) NOT NULL COMMENT '错误类型',
    error_message TEXT COMMENT '错误信息',
    error_stack TEXT COMMENT '错误堆栈',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_execute_id (execute_id),
    INDEX idx_task_id (task_id),
    INDEX idx_error_type (error_type)
);
```

#### 3.5.4 依赖组件
- **MySQL**: 采集数据存储
- **Redis**: 任务队列、去重缓存
- **Elasticsearch**: 全文搜索
- **RabbitMQ**: 消息队列
- **采集配置服务**: 获取任务配置
- **内容审核服务**: 数据审核

### 3.6 内容审核服务 (content-audit-service)

#### 3.6.1 功能职责
- 内容自动审核
- 人工审核工作流
- 审核规则管理
- 敏感词过滤
- 审核结果统计

#### 3.6.2 核心接口设计

**提交审核接口**
```
POST /audit/submit
Request: {
  "dataId": "string",
  "auditType": "AUTO",
  "priority": 1,
  "auditReason": "string"
}
Response: {
  "code": 200,
  "message": "提交成功",
  "data": {
    "auditId": "string",
    "status": "PENDING"
  }
}
```

**审核处理接口**
```
POST /audit/process/{auditId}
Request: {
  "auditResult": "PASS",
  "auditComment": "string",
  "tags": ["string"]
}
Response: {
  "code": 200,
  "message": "处理成功",
  "data": {
    "auditId": "string",
    "status": "COMPLETED"
  }
}
```

**待审核列表接口**
```
GET /audit/pending?page=1&size=10&priority=1&auditType=MANUAL
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 50,
    "list": [{
      "auditId": "string",
      "dataId": "string",
      "title": "string",
      "content": "string",
      "priority": 1,
      "submitTime": "2024-01-01 10:00:00",
      "auditType": "MANUAL",
      "riskLevel": "HIGH"
    }]
  }
}
```

#### 3.6.3 数据库设计

**审核记录表 (audit_record)**
```sql
CREATE TABLE audit_record (
    audit_id VARCHAR(64) PRIMARY KEY COMMENT '审核ID',
    data_id VARCHAR(64) NOT NULL COMMENT '数据ID',
    audit_type TINYINT NOT NULL COMMENT '审核类型 1自动 2人工',
    status TINYINT DEFAULT 1 COMMENT '审核状态 1待审核 2通过 3拒绝 4取消',
    priority TINYINT DEFAULT 3 COMMENT '优先级 1高 2中 3低',
    risk_level TINYINT DEFAULT 1 COMMENT '风险等级 1低 2中 3高',
    submit_time DATETIME NOT NULL COMMENT '提交时间',
    audit_time DATETIME COMMENT '审核时间',
    auditor_id VARCHAR(64) COMMENT '审核人ID',
    audit_result TINYINT COMMENT '审核结果 1通过 2拒绝',
    audit_comment TEXT COMMENT '审核意见',
    audit_reason VARCHAR(500) COMMENT '审核原因',
    auto_audit_score DECIMAL(3,2) COMMENT '自动审核分数',
    tags JSON COMMENT '标签',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_data_id (data_id),
    INDEX idx_status (status),
    INDEX idx_audit_type (audit_type),
    INDEX idx_priority (priority),
    INDEX idx_submit_time (submit_time),
    INDEX idx_auditor_id (auditor_id)
);
```

**审核规则表 (audit_rule)**
```sql
CREATE TABLE audit_rule (
    rule_id VARCHAR(64) PRIMARY KEY COMMENT '规则ID',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    rule_type TINYINT NOT NULL COMMENT '规则类型 1关键词 2正则 3AI模型',
    rule_config JSON NOT NULL COMMENT '规则配置',
    action TINYINT NOT NULL COMMENT '触发动作 1自动通过 2自动拒绝 3转人工',
    priority INT DEFAULT 0 COMMENT '优先级',
    status TINYINT DEFAULT 1 COMMENT '状态 1启用 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_rule_type (rule_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);
```

**敏感词库表 (sensitive_word)**
```sql
CREATE TABLE sensitive_word (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    word VARCHAR(200) NOT NULL COMMENT '敏感词',
    word_type TINYINT NOT NULL COMMENT '词类型 1政治 2色情 3暴力 4广告',
    level TINYINT DEFAULT 1 COMMENT '敏感级别 1低 2中 3高',
    action TINYINT DEFAULT 2 COMMENT '处理动作 1忽略 2标记 3拒绝',
    status TINYINT DEFAULT 1 COMMENT '状态 1启用 0禁用',
    created_by VARCHAR(64) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_word (word),
    INDEX idx_word_type (word_type),
    INDEX idx_level (level),
    INDEX idx_status (status)
);
```

#### 3.6.4 依赖组件
- **MySQL**: 审核数据存储
- **Redis**: 审核队列、规则缓存
- **AI模型服务**: 内容智能审核
- **数据采集服务**: 获取待审核数据
- **通知服务**: 审核结果通知

### 3.7 舆情分析服务 (sentiment-analysis-service)

#### 3.7.1 功能职责
- 情感分析
- 关键词提取
- 热点话题识别
- 趋势分析
- 统计报表生成

#### 3.7.2 核心接口设计

**情感分析接口**
```
POST /analysis/sentiment
Request: {
  "dataIds": ["string"],
  "analysisType": "BATCH"
}
Response: {
  "code": 200,
  "message": "分析成功",
  "data": [{
    "dataId": "string",
    "sentimentScore": 0.75,
    "sentimentLabel": "POSITIVE",
    "confidence": 0.95,
    "keywords": ["关键词1", "关键词2"]
  }]
}
```

**热点话题分析接口**
```
GET /analysis/hotTopics?startTime=xxx&endTime=xxx&limit=10
Response: {
  "code": 200,
  "message": "查询成功",
  "data": [{
    "topicId": "string",
    "topicName": "string",
    "keywords": ["string"],
    "mentionCount": 1000,
    "sentimentScore": 0.6,
    "trendScore": 0.8,
    "relatedData": [{
      "dataId": "string",
      "title": "string",
      "publishTime": "2024-01-01 10:00:00"
    }]
  }]
}
```

**统计报表接口**
```
GET /analysis/statistics?type=DAILY&startTime=xxx&endTime=xxx
Response: {
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalCount": 10000,
    "positiveCount": 6000,
    "negativeCount": 2000,
    "neutralCount": 2000,
    "trendData": [{
      "date": "2024-01-01",
      "count": 500,
      "positiveRate": 0.6,
      "negativeRate": 0.2
    }],
    "topKeywords": [{
      "keyword": "string",
      "count": 100,
      "sentiment": 0.7
    }]
  }
}
```

#### 3.7.3 数据库设计

**情感分析结果表 (sentiment_analysis_result)**
```sql
CREATE TABLE sentiment_analysis_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    data_id VARCHAR(64) NOT NULL COMMENT '数据ID',
    sentiment_score DECIMAL(3,2) NOT NULL COMMENT '情感分数 -1到1',
    sentiment_label VARCHAR(20) NOT NULL COMMENT '情感标签 POSITIVE/NEGATIVE/NEUTRAL',
    confidence DECIMAL(3,2) NOT NULL COMMENT '置信度',
    keywords JSON COMMENT '关键词',
    emotions JSON COMMENT '情绪分析结果',
    analysis_time DATETIME NOT NULL COMMENT '分析时间',
    model_version VARCHAR(50) COMMENT '模型版本',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_data_id (data_id),
    INDEX idx_sentiment_label (sentiment_label),
    INDEX idx_analysis_time (analysis_time)
);
```

**热点话题表 (hot_topic)**
```sql
CREATE TABLE hot_topic (
    topic_id VARCHAR(64) PRIMARY KEY COMMENT '话题ID',
    topic_name VARCHAR(500) NOT NULL COMMENT '话题名称',
    keywords JSON NOT NULL COMMENT '关键词',
    mention_count INT DEFAULT 0 COMMENT '提及次数',
    sentiment_score DECIMAL(3,2) COMMENT '平均情感分数',
    trend_score DECIMAL(3,2) COMMENT '趋势分数',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    peak_time DATETIME COMMENT '峰值时间',
    status TINYINT DEFAULT 1 COMMENT '状态 1活跃 2衰减 3结束',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_mention_count (mention_count),
    INDEX idx_trend_score (trend_score),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
);
```

**话题数据关联表 (topic_data_relation)**
```sql
CREATE TABLE topic_data_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    topic_id VARCHAR(64) NOT NULL COMMENT '话题ID',
    data_id VARCHAR(64) NOT NULL COMMENT '数据ID',
    relevance_score DECIMAL(3,2) NOT NULL COMMENT '相关性分数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_topic_data (topic_id, data_id),
    INDEX idx_topic_id (topic_id),
    INDEX idx_data_id (data_id),
    INDEX idx_relevance_score (relevance_score)
);
```

#### 3.7.4 依赖组件
- **MySQL**: 分析结果存储
- **Redis**: 分析缓存
- **Elasticsearch**: 数据检索
- **AI模型服务**: 情感分析模型
- **数据采集服务**: 获取分析数据

### 3.8 通知服务 (notification-service)

#### 3.8.1 功能职责
- 消息推送
- 邮件通知
- 短信通知
- 系统消息
- 通知模板管理

#### 3.8.2 核心接口设计

**发送通知接口**
```
POST /notification/send
Request: {
  "notificationType": "EMAIL",
  "recipients": ["user1", "user2"],
  "templateId": "string",
  "params": {
    "title": "string",
    "content": "string"
  },
  "priority": 1
}
Response: {
  "code": 200,
  "message": "发送成功",
  "data": {
    "notificationId": "string",
    "status": "SENT"
  }
}
```

#### 3.8.3 数据库设计

**通知记录表 (notification_record)**
```sql
CREATE TABLE notification_record (
    notification_id VARCHAR(64) PRIMARY KEY COMMENT '通知ID',
    notification_type TINYINT NOT NULL COMMENT '通知类型 1邮件 2短信 3推送 4系统消息',
    recipient_id VARCHAR(64) NOT NULL COMMENT '接收人ID',
    recipient_type TINYINT DEFAULT 1 COMMENT '接收人类型 1用户 2角色 3组织',
    title VARCHAR(500) COMMENT '标题',
    content TEXT COMMENT '内容',
    template_id VARCHAR(64) COMMENT '模板ID',
    params JSON COMMENT '参数',
    status TINYINT DEFAULT 1 COMMENT '状态 1待发送 2已发送 3发送失败',
    send_time DATETIME COMMENT '发送时间',
    read_time DATETIME COMMENT '阅读时间',
    priority TINYINT DEFAULT 3 COMMENT '优先级 1高 2中 3低',
    error_message TEXT COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_status (status),
    INDEX idx_send_time (send_time)
);
```

## 4. 系统架构图

### 4.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[管理后台] --> B[API网关]
        A1[移动端] --> B
    end

    subgraph "网关层"
        B --> C[认证授权]
        B --> D[负载均衡]
        B --> E[限流熔断]
    end

    subgraph "微服务层"
        F[用户认证服务]
        G[用户管理服务]
        H[权限管理服务]
        I[采集配置服务]
        J[数据采集服务]
        K[内容审核服务]
        L[舆情分析服务]
        M[通知服务]
    end

    subgraph "数据层"
        N[MySQL集群]
        O[Redis集群]
        P[Elasticsearch]
        Q[消息队列]
    end

    subgraph "基础设施"
        R[服务注册中心]
        S[配置中心]
        T[监控中心]
        U[日志中心]
    end

    B --> F
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K
    B --> L
    B --> M

    F --> N
    F --> O
    G --> N
    G --> O
    H --> N
    H --> O
    I --> N
    I --> O
    J --> N
    J --> O
    J --> P
    J --> Q
    K --> N
    K --> O
    K --> Q
    L --> N
    L --> O
    L --> P
    M --> N
    M --> O
    M --> Q

    F --> R
    G --> R
    H --> R
    I --> R
    J --> R
    K --> R
    L --> R
    M --> R

    F --> S
    G --> S
    H --> S
    I --> S
    J --> S
    K --> S
    L --> S
    M --> S
```

### 4.2 数据流架构图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as API网关
    participant A as 认证服务
    participant C as 采集配置服务
    participant D as 数据采集服务
    participant R as 内容审核服务
    participant S as 舆情分析服务
    participant N as 通知服务

    U->>G: 登录请求
    G->>A: 验证用户
    A-->>G: 返回Token
    G-->>U: 登录成功

    U->>G: 创建采集任务
    G->>C: 保存任务配置
    C->>D: 触发采集任务
    D->>D: 执行数据采集
    D->>R: 提交审核
    R->>R: 自动/人工审核
    R->>S: 审核通过数据
    S->>S: 情感分析
    S->>N: 发送分析报告
    N-->>U: 推送通知
```

## 5. 部署架构

### 5.1 环境规划

#### 5.1.1 开发环境
- **服务器**: 单机部署
- **数据库**: MySQL 8.0 单实例
- **缓存**: Redis 单实例
- **消息队列**: RabbitMQ 单实例

#### 5.1.2 测试环境
- **服务器**: 2台服务器
- **数据库**: MySQL 主从复制
- **缓存**: Redis 主从复制
- **消息队列**: RabbitMQ 集群

#### 5.1.3 生产环境
- **服务器**: 6台服务器（3台应用服务器 + 3台数据服务器）
- **数据库**: MySQL 主从复制 + 读写分离
- **缓存**: Redis 集群（3主3从）
- **消息队列**: RabbitMQ 集群（3节点）
- **搜索引擎**: Elasticsearch 集群（3节点）

### 5.2 容器化部署

#### 5.2.1 Docker配置

**基础镜像Dockerfile**
```dockerfile
FROM openjdk:17-jre-slim
VOLUME /tmp
COPY target/*.jar app.jar
ENTRYPOINT ["java","-jar","/app.jar"]
EXPOSE 8080
```

**docker-compose.yml**
```yaml
version: '3.8'
services:
  # API网关
  gateway:
    image: wenxun/gateway:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - nacos
      - redis

  # 用户认证服务
  auth-service:
    image: wenxun/auth-service:latest
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - mysql
      - redis
      - nacos

  # 数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: wenxun
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  # 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # 服务注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    environment:
      MODE: standalone
    ports:
      - "8848:8848"
    volumes:
      - nacos_data:/home/<USER>/data

volumes:
  mysql_data:
  redis_data:
  nacos_data:
```

### 5.3 监控方案

#### 5.3.1 应用监控
- **Spring Boot Actuator**: 应用健康检查
- **Micrometer**: 指标收集
- **Prometheus**: 指标存储
- **Grafana**: 监控面板

#### 5.3.2 日志监控
- **Logback**: 日志框架
- **ELK Stack**: 日志收集分析
- **Filebeat**: 日志采集
- **Logstash**: 日志处理
- **Elasticsearch**: 日志存储
- **Kibana**: 日志查询分析

#### 5.3.3 链路追踪
- **Spring Cloud Sleuth**: 链路追踪
- **Zipkin**: 链路分析

## 6. 安全方案

### 6.1 认证授权
- **JWT Token**: 无状态认证
- **RBAC模型**: 基于角色的权限控制
- **OAuth2**: 第三方登录支持
- **单点登录**: SSO集成

### 6.2 数据安全
- **数据加密**: 敏感数据AES加密
- **传输加密**: HTTPS/TLS
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入输出过滤

### 6.3 接口安全
- **API限流**: 防止恶意请求
- **签名验证**: 接口调用签名
- **IP白名单**: 限制访问来源
- **防重放攻击**: 时间戳+随机数

## 7. 性能优化

### 7.1 数据库优化
- **索引优化**: 合理创建索引
- **分库分表**: 大表水平拆分
- **读写分离**: 主从复制
- **连接池**: 数据库连接池优化

### 7.2 缓存策略
- **多级缓存**: 本地缓存+分布式缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 数据变更时及时更新缓存
- **缓存穿透**: 布隆过滤器防护

### 7.3 系统优化
- **异步处理**: 耗时操作异步化
- **批量处理**: 减少数据库交互次数
- **连接复用**: HTTP连接池
- **资源压缩**: 静态资源压缩

## 8. 开发规范

### 8.1 代码规范
- **命名规范**: 驼峰命名法
- **注释规范**: 类、方法必须有注释
- **异常处理**: 统一异常处理机制
- **日志规范**: 统一日志格式

### 8.2 接口规范
- **RESTful API**: 遵循REST设计原则
- **统一响应格式**: 标准化响应结构
- **版本控制**: API版本管理
- **文档规范**: Swagger API文档

### 8.3 数据库规范
- **命名规范**: 下划线命名法
- **字段规范**: 必须有注释和默认值
- **索引规范**: 合理创建索引
- **变更规范**: 数据库变更脚本管理

这个架构文档详细描述了舆情监控Admin系统的完整设计方案，包括微服务拆分、接口设计、数据库设计、部署方案等各个方面。您可以根据实际需求对具体细节进行调整和完善。
